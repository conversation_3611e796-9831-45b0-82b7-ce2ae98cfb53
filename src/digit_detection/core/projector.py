"""Visualizer module for dimensionality reduction and projection visualization."""

from dataclasses import dataclass
from enum import Enum, auto
from typing import Dict, List, Literal, Optional, Union, Any, <PERSON><PERSON>

import numpy as np
from numpy.typing import NDArray
import matplotlib.pyplot as plt
from matplotlib.figure import Figure
# Import needed for 3D projection even if not directly referenced
from mpl_toolkits.mplot3d import Axes3D  # noqa: F401
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import umap

from digit_detection.custom_nn.epoch_data import EpochData


class ProjectionMethod(Enum):
    """Supported projection methods."""
    PCA = auto()
    TSNE = auto()
    UMAP = auto()


class ProjectionTarget(Enum):
    """Target data for projection."""
    INPUT = auto()
    OUTPUT = auto()


class ProjectionDimension(Enum):
    """Projection dimensionality."""
    TWO_D = auto()
    THREE_D = auto()

@dataclass
class ProjectionData:
    projected_data: NDArray[np.float64]
    labels: NDArray[np.int64]
    preds: NDArray[np.int64]
    accuracy: float
    epoch: int

class Projector:
    """Visualizer for dimensionality reduction and projection visualization."""

    def create_projection_data(
        self,
        epoch_data: List[EpochData],
        target: ProjectionTarget,
        method: ProjectionMethod,
        dimension: ProjectionDimension,
        sample_count: Optional[int] = None
    ) -> List[ProjectionData]:
        """Create projection data for visualization.

        Args:
            epoch_data: List of epoch data
            target: Projection target (INPUT or OUTPUT)
            method: Projection method (PCA, TSNE, or UMAP)
            dimension: Projection dimension (TWO_D or THREE_D)
            sample_count: Optional number of samples to use

        Returns:
            List of projection data
        """

        projection_data = []
        for epoch, epoch_data in enumerate(epoch_data):
            
            # Select data based on target
            high_dimensional_data = epoch_data.test_input if target == ProjectionTarget.INPUT else epoch_data.test_output_actual
            if sample_count is not None:
                high_dimensional_data = high_dimensional_data[:sample_count]
            
            # Select labels and predicted labels
            labels = epoch_data.test_output_expected
            if sample_count is not None:
                labels = labels[:sample_count]

            predicted_labels = np.argmax(epoch_data.test_output_actual, axis=1)
            if sample_count is not None:
                predicted_labels = predicted_labels[:sample_count]

            projected_data = self.__compute_projection(method, high_dimensional_data, dimension)
            
            projection_data.append(
                ProjectionData(
                    projected_data=projected_data,
                    labels=labels,
                    preds=predicted_labels,
                    accuracy=epoch_data.accuracy,
                    epoch=epoch
                )
            )

        return projection_data

    def __compute_projection(
        self,
        method: ProjectionMethod,
        data: NDArray[np.float64],
        dimension: ProjectionDimension = ProjectionDimension.TWO_D
    ) -> NDArray[np.float64]:
        """Compute projection using the specified method.

        Args:
            method: Projection method
            data: Data to project
            dimension: Projection dimension (TWO_D or THREE_D)

        Returns:
            Projected data
        """
        n_components = 3 if dimension == ProjectionDimension.THREE_D else 2

        if method == ProjectionMethod.PCA:
            reducer = PCA(n_components=n_components)
        elif method == ProjectionMethod.TSNE:
            reducer = TSNE(n_components=n_components, perplexity=30, random_state=42)
        elif method == ProjectionMethod.UMAP:
            reducer = umap.UMAP(n_components=n_components, random_state=42)
        else:
            raise ValueError(f"Unsupported projection method: {method}")

        # Cast the result to ensure correct type annotation
        result: NDArray[np.float64] = reducer.fit_transform(data)
        return result

    def visualize_projection(
        self,
        projection_data: Dict[str, Any],
        title: Optional[str] = None,
        figsize: Tuple[int, int] = (10, 8),
        dimension: Union[str, ProjectionDimension] = ProjectionDimension.TWO_D,
        colormap: str = "viridis",
        alpha: float = 0.7,
        point_size: int = 30,
    ) -> Figure:
        """Visualize projection data.

        Args:
            projection_data: Projection data from create_projection
            title: Plot title
            figsize: Figure size
            dimension: Projection dimension (TWO_D or THREE_D)
            colormap: Colormap for scatter plot
            alpha: Alpha value for scatter plot
            point_size: Size of scatter points

        Returns:
            Matplotlib figure
        """
        # Convert string input to enum type if needed
        if isinstance(dimension, str):
            dimension = dimension.upper()
            try:
                dimension = ProjectionDimension[dimension]
            except KeyError:
                raise ValueError(f"Invalid projection dimension: {dimension}. Must be 'TWO_D' or 'THREE_D'")
        x_projected = projection_data["x_projected"]
        labels = projection_data["labels"]
        correct = projection_data["correct"]
        accuracy = projection_data["accuracy"]
        epoch = projection_data["epoch"]

        if title is None:
            title = f"Epoch {epoch} - Accuracy: {accuracy:.2f}%"

        # Create figure
        fig = plt.figure(figsize=figsize)

        if dimension == ProjectionDimension.THREE_D and x_projected.shape[1] >= 3:
            # Axes3D imported at module level
            ax = fig.add_subplot(111, projection='3d')
            # Create scatter plot with explicit parameters to avoid mypy error
            scatter_kwargs = {
                'x': x_projected[:, 0],
                'y': x_projected[:, 1],
                'z': x_projected[:, 2],
                'c': labels,
                'cmap': colormap,
                'alpha': alpha,
                's': point_size,
                'edgecolors': 'w' if alpha < 1.0 else None
            }
            scatter = ax.scatter(**scatter_kwargs)
            ax.set_xlabel('Component 1')
            ax.set_ylabel('Component 2')
            # Set z-label only for 3D axes
            if hasattr(ax, 'set_zlabel'):
                ax.set_zlabel('Component 3')
        else:
            ax = fig.add_subplot(111)
            # Create scatter plot with explicit parameters to avoid mypy error
            scatter_kwargs = {
                'x': x_projected[:, 0],
                'y': x_projected[:, 1],
                'c': labels,
                'cmap': colormap,
                'alpha': alpha,
                's': point_size,
                'edgecolors': 'w' if alpha < 1.0 else None
            }
            scatter = ax.scatter(**scatter_kwargs)
            ax.set_xlabel('Component 1')
            ax.set_ylabel('Component 2')

        # Add colorbar
        cbar = plt.colorbar(scatter)
        cbar.set_label('Digit Class')

        # Mark incorrect predictions
        if not all(correct):
            incorrect = ~correct
            # Create scatter plot for incorrect predictions
            incorrect_kwargs = {
                'x': x_projected[incorrect, 0],
                'y': x_projected[incorrect, 1],
                'facecolors': 'none',
                'edgecolors': 'red',
                's': point_size*1.5,
                'linewidths': 1.5,
                'alpha': 0.8
            }
            ax.scatter(**incorrect_kwargs)

        ax.set_title(title)
        fig.tight_layout()

        return fig

    @classmethod
    def from_epoch_data(
        cls,
        epoch_data: List[EpochData],
        model: Any,
        feature_model: Optional[Any] = None,
    ) -> "Projector":
        """Create visualizer from epoch data.

        Args:
            epoch_data: List of epoch data
            model: Trained model for predictions
            feature_model: Optional feature extraction model

        Returns:
            Visualizer instance
        """
        # Use the last epoch data for initialization
        last_epoch = epoch_data[-1]

        return cls(
            x_raw=last_epoch.test_input,
            y_true=last_epoch.test_output_expected,
            model=model,
            feature_model=feature_model,
        )
