"""Example showing how to create and display training videos in Streamlit."""

import streamlit as st
import os
from pathlib import Path

from digit_detection.core.activator_type import ActivatorType
from digit_detection.core.cost_type import CostType
from digit_detection.core.dataset_type import DatasetType
from digit_detection.core.flavour_type import FlavourType
from digit_detection.core.model import ModelConfig, DataLoaderFactory
from digit_detection.core.model_factory import ModelFactory
from digit_detection.core.projector import Projector, ProjectionMethod, ProjectionTarget, ProjectionDimension
from digit_detection.core.visualizer import create_streamlit_video, render_training_scene


def create_training_animation():
    """Create training animation and return video path."""
    
    # Create model configuration
    model_config = ModelConfig(
        layer_sizes=[784, 64, 10],
        activator_types=[ActivatorType.SIGMOID, ActivatorType.SIGMOID],
        cost_type=CostType.MSE,
        learning_rate=0.1,
        batch_size=32,
        epochs=3,  # Small number for demo
        flavor=FlavourType.CUSTOM,
        random_seed=42
    )

    # Create and build model
    with st.spinner("Building model..."):
        model = ModelFactory.create_model(model_config)

    # Load data
    with st.spinner("Loading MNIST data..."):
        data_loader = DataLoaderFactory.create_data_loader(DatasetType.MNIST)
        train_data, test_data = data_loader.load_data()

    # Train model
    with st.spinner("Training model..."):
        epoch_data = model.train(train_data, test_data)

    # Create projector
    with st.spinner("Creating projections..."):
        projector = Projector.from_epoch_data(
            epoch_data=epoch_data,
            model=model
        )

        # Create projection data
        projection_data = projector.create_projection_data(
            epoch_data=epoch_data,
            target=ProjectionTarget.INPUT,
            method=ProjectionMethod.PCA,
            dimension=ProjectionDimension.TWO_D,
            sample_count=300  # Limit for faster processing
        )

    # Create animation
    with st.spinner("Generating animation..."):
        video_path = create_streamlit_video(
            projection_data=projection_data,
            use_3d=False,
            quality="medium"
        )

    return video_path


def main():
    """Main Streamlit app function."""
    st.title("Neural Network Training Visualization")
    st.write("Watch how neural network learns to classify MNIST digits through training epochs")

    # Sidebar controls
    st.sidebar.header("Animation Controls")
    
    # Quality selection
    quality = st.sidebar.selectbox(
        "Video Quality",
        options=["low", "medium", "high"],
        index=1,  # Default to medium
        help="Higher quality takes longer to render"
    )
    
    # 3D option
    use_3d = st.sidebar.checkbox(
        "Use 3D Visualization",
        value=False,
        help="3D visualization is more visually appealing but slower to render"
    )
    
    # Projection method
    projection_method = st.sidebar.selectbox(
        "Projection Method",
        options=[ProjectionMethod.PCA, ProjectionMethod.TSNE, ProjectionMethod.UMAP],
        format_func=lambda x: x.name,
        help="Method for dimensionality reduction"
    )
    
    if st.sidebar.button("Generate New Animation", type="primary"):
        try:
            # Create model configuration
            model_config = ModelConfig(
                layer_sizes=[784, 64, 10],
                activator_types=[ActivatorType.SIGMOID, ActivatorType.SIGMOID],
                cost_type=CostType.MSE,
                learning_rate=0.1,
                batch_size=32,
                epochs=3,
                flavor=FlavourType.CUSTOM,
                random_seed=42
            )

            # Build and train model
            with st.spinner("Building and training model..."):
                model = ModelFactory.create_model(model_config)
                data_loader = DataLoaderFactory.create_data_loader(DatasetType.MNIST)
                train_data, test_data = data_loader.load_data()
                epoch_data = model.train(train_data, test_data)

            # Create projections
            with st.spinner("Creating projections..."):
                projector = Projector.from_epoch_data(epoch_data=epoch_data, model=model)
                
                dimension = ProjectionDimension.THREE_D if use_3d else ProjectionDimension.TWO_D
                projection_data = projector.create_projection_data(
                    epoch_data=epoch_data,
                    target=ProjectionTarget.INPUT,
                    method=projection_method,
                    dimension=dimension,
                    sample_count=300
                )

            # Generate video
            with st.spinner("Generating animation..."):
                video_path = create_streamlit_video(
                    projection_data=projection_data,
                    use_3d=use_3d,
                    quality=quality
                )
                
            st.session_state.video_path = video_path
            st.session_state.video_info = {
                'quality': quality,
                'use_3d': use_3d,
                'method': projection_method.name,
                'epochs': len(projection_data),
                'samples': len(projection_data[0].projected_data) if projection_data else 0
            }
            st.success("Animation generated successfully!")
            
        except Exception as e:
            st.error(f"Error generating animation: {str(e)}")
            st.exception(e)

    # Display video if available
    if hasattr(st.session_state, 'video_path') and os.path.exists(st.session_state.video_path):
        st.subheader("Training Animation")
        
        # Show video info
        if hasattr(st.session_state, 'video_info'):
            info = st.session_state.video_info
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Quality", info['quality'].title())
            with col2:
                st.metric("Projection", "3D" if info['use_3d'] else "2D")
            with col3:
                st.metric("Method", info['method'])
            with col4:
                st.metric("Epochs", info['epochs'])
        
        st.write("**Legend:**")
        st.write("- **Colors** represent the true digit labels (0-9)")
        st.write("- **White borders** indicate incorrect predictions")
        st.write("- **Accuracy graph** shows training progress")
        
        # Display the video
        st.video(st.session_state.video_path)
        
        # Show file info
        video_path = Path(st.session_state.video_path)
        if video_path.exists():
            file_size = video_path.stat().st_size / (1024 * 1024)  # MB
            st.caption(f"Video file: {video_path.name} ({file_size:.1f} MB)")
            
        # Download button
        with open(st.session_state.video_path, 'rb') as video_file:
            st.download_button(
                label="Download Video",
                data=video_file.read(),
                file_name="training_animation.mp4",
                mime="video/mp4"
            )
    else:
        st.info("Click 'Generate New Animation' to create a training visualization")

    # Instructions
    st.subheader("How to Use")
    st.markdown("""
    1. **Configure settings** in the sidebar (quality, 3D mode, projection method)
    2. Click **Generate New Animation** to train a neural network and create a visualization
    3. The animation shows how the network learns to separate different digits over training epochs
    4. Watch how points move and cluster as the network improves its predictions
    5. The accuracy graph shows the quantitative improvement over time
    
    **Performance Tips:**
    - Use **low quality** for faster rendering during testing
    - **2D projections** render faster than 3D
    - **PCA** is fastest, **UMAP** is slowest but often most visually appealing
    """)

    # Technical details
    with st.expander("Technical Details"):
        st.markdown("""
        **Model Architecture:**
        - Input: 784 features (28x28 MNIST images)
        - Hidden: 64 neurons with sigmoid activation
        - Output: 10 neurons (digit classes)
        
        **Visualization:**
        - Sample size: 300 points for performance
        - Animation: Created with Manim library
        - Video format: MP4 optimized for web playback
        
        **Quality Settings:**
        - **Low**: 480p, 15fps - Fast rendering
        - **Medium**: 720p, 24fps - Good balance
        - **High**: 1080p, 30fps - Best quality
        """)


if __name__ == "__main__":
    main()
