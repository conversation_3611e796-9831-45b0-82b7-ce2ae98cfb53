"""Example demonstrating video rendering with the ModelTrainingScene."""

from digit_detection.core.activator_type import ActivatorType
from digit_detection.core.cost_type import CostType
from digit_detection.core.dataset_type import DatasetType
from digit_detection.core.flavour_type import FlavourType
from digit_detection.core.model import ModelConfig, DataLoaderFactory
from digit_detection.core.model_factory import ModelFactory
from digit_detection.core.projector import Projector, ProjectionMethod, ProjectionTarget, ProjectionDimension
from digit_detection.core.visualizer import render_training_scene, create_streamlit_video


def main():
    """Main function demonstrating video rendering."""
    print("Video Rendering Example")
    print("=" * 30)

    # Create model configuration
    model_config = ModelConfig(
        layer_sizes=[784, 128, 64, 10],
        activator_types=[ActivatorType.SIGMOID, ActivatorType.SIGMOID, ActivatorType.SIGMOID],
        cost_type=CostType.MSE,
        learning_rate=0.1,
        batch_size=32,
        epochs=5,  # Small number for quick demo
        flavor=FlavourType.CUSTOM,
        random_seed=42
    )

    # Create and build model
    print("Building model...")
    model = ModelFactory.create_model(model_config)

    # Load data
    print("Loading MNIST data...")
    data_loader = DataLoaderFactory.create_data_loader(DatasetType.MNIST)
    train_data, test_data = data_loader.load_data()

    # Train model and collect epoch data
    print("Training model...")
    epoch_data = model.train(train_data, test_data)

    print(f"Training completed. Collected {len(epoch_data)} epochs of data.")

    # Create projector
    print("Creating projector...")
    projector = Projector.from_epoch_data(
        epoch_data=epoch_data,
        model=model
    )

    # Create projection data for animation
    print("Creating projection data...")
    projection_data = projector.create_projection_data(
        epoch_data=epoch_data,
        target=ProjectionTarget.INPUT,
        method=ProjectionMethod.PCA,
        dimension=ProjectionDimension.TWO_D,
        sample_count=500  # Limit samples for faster animation
    )

    print(f"Created projection data for {len(projection_data)} epochs")

    # Example 1: Basic video rendering
    print("\n1. Creating basic training video...")
    video_path_basic = render_training_scene(
        projection_data=projection_data,
        output_file="./videos/basic_training.mp4",
        quality="medium",
        use_3d=False
    )
    print(f"Basic video saved to: {video_path_basic}")

    # Example 2: High quality 3D video
    print("\n2. Creating 3D projection data...")
    projection_data_3d = projector.create_projection_data(
        epoch_data=epoch_data,
        target=ProjectionTarget.INPUT,
        method=ProjectionMethod.PCA,
        dimension=ProjectionDimension.THREE_D,
        sample_count=500
    )

    print("Creating high-quality 3D video...")
    video_path_3d = render_training_scene(
        projection_data=projection_data_3d,
        output_file="./videos/3d_training.mp4",
        quality="high",
        use_3d=True
    )
    print(f"3D video saved to: {video_path_3d}")

    # Example 3: Streamlit-optimized video
    print("\n3. Creating Streamlit-optimized video...")
    streamlit_video_path = create_streamlit_video(
        projection_data=projection_data,
        use_3d=False,
        quality="medium"
    )
    print(f"Streamlit video saved to: {streamlit_video_path}")

    # Example 4: Different projection methods
    print("\n4. Creating videos with different projection methods...")
    
    methods = [
        (ProjectionMethod.TSNE, "tsne"),
        (ProjectionMethod.UMAP, "umap")
    ]
    
    for method, name in methods:
        print(f"Creating {name.upper()} projection...")
        proj_data = projector.create_projection_data(
            epoch_data=epoch_data,
            target=ProjectionTarget.INPUT,
            method=method,
            dimension=ProjectionDimension.TWO_D,
            sample_count=300  # Smaller sample for slower methods
        )
        
        video_path = render_training_scene(
            projection_data=proj_data,
            output_file=f"./videos/{name}_training.mp4",
            quality="low",  # Use low quality for faster rendering
            use_3d=False
        )
        print(f"{name.upper()} video saved to: {video_path}")

    print("\nAll videos created successfully!")
    print("\nTo use in Streamlit:")
    print("import streamlit as st")
    print("st.video('path/to/video.mp4')")
    
    print("\nVideo files created:")
    print("- basic_training.mp4 (2D PCA, medium quality)")
    print("- 3d_training.mp4 (3D PCA, high quality)")
    print("- tsne_training.mp4 (2D t-SNE, low quality)")
    print("- umap_training.mp4 (2D UMAP, low quality)")
    print("- training_visualization.mp4 (Streamlit optimized)")


if __name__ == "__main__":
    main()
